#define MyAppName "DaiZhong Novel"
#define MyAppVersion "4.2.5.1"
#define MyAppPublisher "DaiZhong"
#define MyAppExeName "novel_app.exe"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocExt ".novel"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; Application ID - unique identifier
AppId={{F8B0A845-5C7F-4FE3-A6B8-D42F03D7F0C1}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL=https://www.daizhong.com/
AppSupportURL=https://www.daizhong.com/
AppUpdatesURL=https://www.daizhong.com/

; Default installation directory
DefaultDirName={autopf}\{#MyAppName}
DefaultGroupName={#MyAppName}

; Disable directory selection button on welcome page
DisableDirPage=no
DisableProgramGroupPage=yes

; Output directory and filename
OutputDir=D:\project\vs code\novel_app\build\windows\installer
OutputBaseFilename=DaiZhong_Novel_Setup_4.2.5.1

; Compression settings
Compression=lzma
SolidCompression=yes

; Installer appearance settings
WizardStyle=modern
SetupIconFile=D:\project\vs code\novel_app\windows\runner\resources\app_icon.ico

; Only support 64-bit Windows
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; Main program files - use Release version
Source: "D:\project\vs code\novel_app\build\windows\x64\runner\Release\novel_app.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\project\vs code\novel_app\build\windows\x64\runner\Release\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; Include VC++ runtime installer
Source: "D:\project\vs code\novel_app\windows\installer\VC_redist.x64.exe"; DestDir: "{tmp}"; Flags: deleteafterinstall; Check: VCRedistNeedsInstall

; Add data directory
Source: "D:\project\vs code\novel_app\data\*"; DestDir: "{app}\data"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; Add necessary DLL files
Source: "D:\project\vs code\novel_app\windows\flutter\ephemeral\flutter_windows.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "D:\project\vs code\novel_app\windows\flutter\ephemeral\icudtl.dat"; DestDir: "{app}\data"; Flags: ignoreversion

; Add plugin DLLs (won't fail even if directory doesn't exist)
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; Explicitly specify missing plugin DLLs (won't fail even if directory doesn't exist)
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\permission_handler_windows\*"; DestDir: "{app}\plugins\permission_handler_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\just_audio_windows\*"; DestDir: "{app}\plugins\just_audio_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\url_launcher_windows\*"; DestDir: "{app}\plugins\url_launcher_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\share_plus\*"; DestDir: "{app}\plugins\share_plus"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\file_selector_windows\*"; DestDir: "{app}\plugins\file_selector_windows"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

; Directly copy specific DLL files to application directory (won't fail even if file doesn't exist)
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\permission_handler_windows\Release\permission_handler_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\just_audio_windows\Release\just_audio_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\url_launcher_windows\Release\url_launcher_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\share_plus\Release\share_plus_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "D:\project\vs code\novel_app\build\windows\x64\plugins\file_selector_windows\Release\file_selector_windows_plugin.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
; Install VC++ runtime
Filename: "{tmp}\vc_redist.x64.exe"; Parameters: "/install /quiet /norestart"; StatusMsg: "Installing VC++ runtime..."; Flags: waituntilterminated; Check: VCRedistNeedsInstall

; Run application after installation
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Code]
// Check if VC++ runtime needs to be installed
function VCRedistNeedsInstall: Boolean;
var
  Version: String;
  Major, Minor, Bld, Rbld: Cardinal;
  RegKey: String;
begin
  // Initialize result
  Result := True;
  
  // Check if we're on 64-bit Windows
  if IsWin64 then
    RegKey := 'SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64'
  else
    RegKey := 'SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86';

  // Check if VC++ runtime is already installed
  if RegQueryStringValue(HKEY_LOCAL_MACHINE, RegKey, 'Version', Version) then
  begin
    // Parse version string
    if ParseVersion(Version, Major, Minor, Bld, Rbld) then
    begin
      // Check if version is at least 14.0.23026.0 (VS 2015 Update 3)
      if (Major > 14) or
         ((Major = 14) and (Minor > 0)) or
         ((Major = 14) and (Minor = 0) and (Bld >= 23026)) then
      begin
        Result := False;
      end;
    end;
  end;
end;

// Actions after installation
procedure CurStepChanged(CurStep: TSetupStep);
var
  AppDir: String;
begin
  if CurStep = ssPostInstall then
  begin
    AppDir := ExpandConstant('{app}');

    // Create necessary data directories
    ForceDirectories(AppDir + '\data');
    ForceDirectories(AppDir + '\data\db');
    ForceDirectories(AppDir + '\data\temp');

    // Ensure plugin directories exist
    ForceDirectories(AppDir + '\plugins');
    ForceDirectories(AppDir + '\plugins\permission_handler_windows');
    ForceDirectories(AppDir + '\plugins\just_audio_windows');
    ForceDirectories(AppDir + '\plugins\url_launcher_windows');
    ForceDirectories(AppDir + '\plugins\share_plus');
    ForceDirectories(AppDir + '\plugins\file_selector_windows');
  end;
end;

package com.daizhong.novelapp;

import androidx.annotation.NonNull;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugins.GeneratedPluginRegistrant;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.EventChannel;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.content.pm.PackageManager;
import android.app.Activity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.os.PowerManager;
import android.content.Context;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";
    private static final String CHANNEL = "com.daizhong.novelapp/app_channel";
    private static final String UPDATE_CHANNEL = "com.daizhong.novelapp/update";
    private static final String UPDATE_EVENT_CHANNEL = "com.daizhong.novelapp/update_events";
    private static final String PERMISSION_CHANNEL = "com.daizhong.novelapp/permission";
    private static final String BACKGROUND_SERVICE_CHANNEL = "com.daizhong.novelapp/background_service";
    private static final int REQUEST_PERMISSION_CODE = 1001;
    private static final int REQUEST_IGNORE_BATTERY_OPTIMIZATIONS = 1002;

    private PermissionManager permissionManager;
    private BackgroundServiceManager backgroundServiceManager;

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        GeneratedPluginRegistrant.registerWith(flutterEngine);

        // 初始化管理器
        permissionManager = new PermissionManager(this);
        backgroundServiceManager = new BackgroundServiceManager(this);

        // 设置方法通道
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
            .setMethodCallHandler((call, result) -> {
                // 处理通用方法调用
                switch (call.method) {
                    case "getPlatformVersion":
                        result.success(Build.VERSION.RELEASE);
                        break;
                    case "requestBatteryOptimizationPermission":
                        requestBatteryOptimizationPermission();
                        result.success(true);
                        break;
                    default:
                        result.notImplemented();
                        break;
                }
            });

        // 更新相关方法通道由Kotlin版MainActivity处理

        // 设置权限相关方法通道
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), PERMISSION_CHANNEL)
            .setMethodCallHandler(permissionManager::handleMethodCall);

        // 设置后台服务相关方法通道
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), BACKGROUND_SERVICE_CHANNEL)
            .setMethodCallHandler(backgroundServiceManager::handleMethodCall);

        // 更新事件通道由Kotlin版MainActivity处理
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "MainActivity onCreate");
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "MainActivity onResume");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "MainActivity onDestroy");
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        permissionManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_IGNORE_BATTERY_OPTIMIZATIONS) {
            Log.d(TAG, "Battery optimization request result: " + resultCode);
        }
    }

    // 请求忽略电池优化权限
    private void requestBatteryOptimizationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
            String packageName = getPackageName();
            if (!powerManager.isIgnoringBatteryOptimizations(packageName)) {
                Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
                intent.setData(Uri.parse("package:" + packageName));
                startActivityForResult(intent, REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            }
        }
    }
}

import 'package:langchain/langchain.dart';

/// 小说生成的提示词模板集合
class NovelPromptTemplates {
  /// 大纲生成提示模板 - 要求 JSON 数组输出 (支持分批)
  static final PromptTemplate outlineTemplate = PromptTemplate.fromTemplate('''
你是一位专业的中文小说策划师。请使用中文回复。
任务：根据以下提供的小说信息，为第 {startChapter} 章到第 {endChapter} 章（包含两端）创作小说大纲。

**重要提示：**
*   请严格按照指定的 JSON 格式输出，必须使用中文内容。
*   输出内容**仅包含一个 JSON 数组**，数组中包含从 `{startChapter}` 到 `{endChapter}` 的所有章节对象。
*   **不要包含任何 JSON 数组之外的文本、解释、注释或代码块标记 (```json ... ```)。**
*   **必须直接输出JSON数组，不要添加任何前言、解释或其他文字。**
*   为每一章提供明确的章节编号 (`chapterNumber`)、章节标题 (`chapterTitle`) 和详细的内容概要 (`summary`)。
*   确保 `chapterNumber` 是整数，`chapterTitle` 和 `summary` 是字符串，且内容必须是中文。

**小说信息:**
*   标题: {novelTitle}
*   类型: {genres}
*   主题: {theme}
*   目标读者: {targetReaders}
*   总章节数: {totalChapters}
*   故事背景: {background}
*   其他要求: {otherRequirements}
*   角色设定:
{characters}
*   写作风格参考:
{writingStylePrompt}
*   需融合的知识:
{knowledgeBase}

**输出 JSON 格式示例 (仅数组):**
[
  {{
    "chapterNumber": {startChapter},
    "chapterTitle": "第 {startChapter} 章的标题",
    "summary": "第 {startChapter} 章的详细内容概要..."
  }},
  // ... 中间章节 ...
  {{
    "chapterNumber": {endChapter},
    "chapterTitle": "第 {endChapter} 章的标题",
    "summary": "第 {endChapter} 章的详细内容概要..."
  }}
]

请现在仅生成从第 {startChapter} 章到第 {endChapter} 章的中文JSON数组内容，直接输出JSON数组，不要添加任何其他文字。
''');

  /// 章节细纲生成提示模板 - 针对单个章节
  static final PromptTemplate detailedOutlinePrompt =
      PromptTemplate.fromTemplate('''
你是一位专业的小说编辑和策划师。
任务：根据以下提供的小说整体设定和指定章节的初步信息，为**第 {chapterNumber} 章**生成详细的情节细纲。

**小说整体设定:**
*   标题: {novelTitle}
*   类型: {genres}
*   主题: {theme}
*   目标读者: {targetReaders}
*   故事背景: {background}
*   角色设定:
{characters}
*   文风参考:
{writingStylePrompt}
*   其他要求: {otherRequirements}
*   知识库信息:
{knowledgeBase}

**当前章节信息:**
*   章节编号: {chapterNumber}
*   章节标题: {chapterTitle}
*   章节概要 (来自整体大纲): {chapterSummary}

**详细细纲要求:**
请为**第 {chapterNumber} 章**生成详细的情节梗概，直接输出 Markdown 格式的文本，包含以下部分：

### 章节摘要
[此处填写本章核心内容的简要概述 (1-2句话)]

### 关键情节
- [按顺序描述本章发生的主要事件、冲突和转折点]
- [例如：角色A遇到了挑战B]
- [例如：情节发生转折，揭示了秘密C]

### 场景设定
- [描述本章涉及的主要场景和环境氛围]
- [例如：阴暗的森林深处，气氛紧张]

### 涉及角色
- [列出本章出现的关键角色及其主要互动或状态变化]
- [例如：主角 (探索)，反派 (阻挠)，新角色D (引入)]

### 视角
[指定本章的主要叙事视角，例如：主角第一人称 / 第三人称限制 (主角视角) / 第三人称上帝视角 等]


**输出要求:**
*   请**只输出**为第 {chapterNumber} 章生成的 Markdown 格式的详细细纲文本。
*   **不要包含**任何额外的解释、标题、章节编号或格式标记（如 ```markdown ... ```）。
*   确保内容详尽、逻辑清晰，能够指导后续的章节内容创作。

请现在开始为第 {chapterNumber} 章生成详细细纲文本。
''');

  /// 生成章节内容的提示模板
  static final PromptTemplate chapterTemplate = PromptTemplate.fromTemplate('''
你是一位专业的小说创作助手，请根据以下信息生成一个高质量的小说章节。

# 创作要求
- 小说标题：{{novelTitle}}
- 小说类型：{{genres}}
- 小说主题：{{theme}}
- 目标读者：{{targetReaders}}
- 当前章节：第{{chapterNumber}}章《{{chapterTitle}}》

# 核心创作指令
**你必须严格按照以下章节细纲来创作本章的详细内容。细纲中的情节要点和顺序必须得到体现。**
章节细纲：
{{outlineContent}}

{{#background}}
# 故事背景
{{background}}
{{/background}}

{{#otherRequirements}}
# 其他要求
{{otherRequirements}}
{{/otherRequirements}}

{{#storedOutline}}
# 小说整体大纲
{{storedOutline}}
{{/storedOutline}}

{{#history}}
# 上下文回顾 (历史章节概要)
{{history}}
{{/history}}

{{#previousChapterSummary}}
# 上一章摘要
{{previousChapterSummary}}
{{/previousChapterSummary}}

{{#characters}}
# 角色设定
{{characters}}
{{/characters}}

{{#writingStyleName}}
# 写作风格要求
请严格按照以下文风进行创作：
- 文风名称：{{writingStyleName}}
- 文风描述：{{writingStyleDescription}}
- 文风示例：
{{writingStyleSamples}}
{{/writingStyleName}}

{{#knowledgeBase}}
# 专业知识
请将以下专业知识融入章节内容：
{{knowledgeBase}}
{{/knowledgeBase}}

# 创作指南
1. 请直接创作章节正文，无需添加章节标题或编号
2. 章节内容要贴合上方提供的核心创作指令中的章节细纲要求
3. 保持人物性格、世界观设定的一致性
4. 文字要生动流畅，情节要引人入胜
5. 多运用细节描写和对话，少用总结性语言
6. 章节字数控制在3000-5000字之间
7. 如有对话，请使用"xxxx"某某说道的格式
8. 禁止使用小标题、分段标记或编号
9. 禁止添加作者注、编者按等内容

请现在开始创作第{{chapterNumber}}章的内容。
''');

  /// 续写小说的提示模板
  static final PromptTemplate continueTemplate = PromptTemplate.fromTemplate('''
你是一位专业的小说创作助手，请基于已有内容续写小说。

# 创作要求
- 小说标题：{{novelTitle}}
- 小说类型：{{genres}}
- 目标读者：{{targetReaders}}
- 续写章节数：{{chapterCount}}
- 续写提示：{{continuePrompt}}

# 已有内容
{{existingContent}}

{{#writingStyleName}}
# 写作风格要求
请严格按照以下文风进行创作：
- 文风名称：{{writingStyleName}}
- 文风描述：{{writingStyleDescription}}
- 文风示例：
{{writingStyleSamples}}
{{/writingStyleName}}

{{#knowledgeBase}}
# 专业知识
请将以下专业知识融入续写内容：
{{knowledgeBase}}
{{/knowledgeBase}}

# 创作指南
1. 请生成续写的章节大纲，包括章节标题和内容概要
2. 续写内容必须与已有内容保持一致性，包括人物、世界观和情节发展
3. 章节大纲格式如下：

第X章：章节标题
章节内容概要...

第X+1章：章节标题
章节内容概要...

4. 请为{{chapterCount}}个新章节提供完整大纲
5. 确保续写情节的连贯性和逻辑性，避免与已有内容产生矛盾

请现在开始创作续写章节的大纲。
''');

  /// 生成短篇小说的提示模板
  static final PromptTemplate shortNovelTemplate =
      PromptTemplate.fromTemplate('''
你是一位专业的中文短篇小说创作助手，请使用中文创作。请根据以下信息创作短篇小说内容。

# 创作要求
- 小说标题：{novelTitle}
- 小说类型：{genres}
- 主题内容：{theme}
- 目标读者：{targetReaders}
- 目标字数：约{wordCount}字
- 故事背景：{background}
- 其他要求：{otherRequirements}

# 当前生成部分
- 当前部分：第{partNumber}部分（共{totalParts}部分）
- 进度范围：{startPercentage}% - {endPercentage}%

# 当前部分大纲概要
{currentPartSummary}

# 完整小说大纲
{outline}

# 前文内容
{history}

# 角色设定
{characters}

# 写作风格要求
{writingStylePrompt}

# 专业知识
{knowledgeBase}

# 创作指南
1. 请直接创作小说内容，无需添加标题或章节编号
2. 这是分段生成，请严格按照当前部分的大纲概要生成内容
3. 确保与前文内容逻辑连贯，人物性格一致
4. 生成约{wordCount}字的内容
5. 保持人物形象和情节的一致性
6. 文字要生动流畅，情节要引人入胜
7. 多运用细节描写和对话，少用总结性语言
8. 如有对话，请使用"xxxx"某某说道的格式
9. 禁止使用小标题、分段标记或编号
10. 禁止添加作者注、编者按等内容

# 具体创作指令
{input}

请现在开始创作。
''');
}
